using Arabdt.Talmatha.Api.Base;
using Arabdt.Talmatha.Application.Features.Company.Commands.AddCompany;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Arabdt.Talmatha.Api.Controllers
{
    [Authorize]
    [ApiController]
    [Route(Routes.API)]
    public class CompanyController : BaseController
    {
        public CompanyController(IMediator mediator)
            : base(mediator)
        { }

        [HttpPost]
        public async Task<IActionResult> Add([FromBody] AddCompanyCommand addCompanyCommand)
        {
            return Ok(await Mediator.Send(addCompanyCommand));
        }
    }
}
