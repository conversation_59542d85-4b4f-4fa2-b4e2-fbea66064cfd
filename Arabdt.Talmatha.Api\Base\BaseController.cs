﻿using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Arabdt.Talmatha.Api.Base
{
    public class BaseController : ControllerBase
    {
        protected IMediator Mediator { get; }
        //protected ITokenInfoHandler JWT { get; }
        //public BaseController(IMediator mediator, ITokenInfoHandler jwt)
        //{
        //    Mediator = mediator;
        //    JWT = jwt;
        //}
        public BaseController(IMediator mediator)
        {
            Mediator = mediator;
        }
    }
}
