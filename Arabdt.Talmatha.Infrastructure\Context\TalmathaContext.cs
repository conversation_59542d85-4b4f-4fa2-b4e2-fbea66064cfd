﻿using System.Data;
using Arabdt.Talmatha.Domain.Entities;
using Arabdt.Talmatha.Infrastructure.EntityConfigurations;
using Arabdt.Talmatha.SharedKernel;

namespace Arabdt.Talmatha.Infrastructure.Context
{
    public class TalmathaContext : DbContext, IUnitOfWork
    {
        #region DBSet
        public DbSet<Company> Companies { get; set; }
        #endregion

        #region CTRS
        public TalmathaContext(DbContextOptions<TalmathaContext> options) : base(options)
        {
        }
        #endregion

        #region Model Creation
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new CompanyEntityTypeConfiguration());
            modelBuilder.BuildEnums();
        }
        #endregion

        #region Save Object
        public async Task<bool> SaveEntitiesAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
                        // After executing this line all the changes (from the Command Handler and Domain Event Handlers) 
            // performed through the DbContext will be committed
            var result = await base.SaveChangesAsync(cancellationToken) > default(int);

           //await _mediator.DispatchDomainEventsAsync(this, DomainEventFireMode.AfterUpdate);

            return result;
        }
        #endregion

        #region Transaction
        private IDbContextTransaction _currentTransaction;
        public IDbContextTransaction GetCurrentTransaction() => _currentTransaction;
        public bool HasActiveTransaction => _currentTransaction != null;

        public async Task<IDbContextTransaction> BeginTransactionAsync()
        {
            if (_currentTransaction != null) return null;

            _currentTransaction = await Database.BeginTransactionAsync(IsolationLevel.ReadCommitted);

            return _currentTransaction;
        }

        public async Task CommitTransactionAsync(IDbContextTransaction transaction)
        {
            if (transaction == null) throw new ArgumentNullException(nameof(transaction));
            if (transaction != _currentTransaction) throw new InvalidOperationException($"Transaction {transaction.TransactionId} is not current");

            try
            {
                await SaveChangesAsync();
                transaction.Commit();
            }
            catch
            {
                RollbackTransaction();
                throw;
            }
            finally
            {
                if (_currentTransaction != null)
                {
                    _currentTransaction.Dispose();
                    _currentTransaction = null;
                }
            }
        }

        public void RollbackTransaction()
        {
            try
            {
                _currentTransaction?.Rollback();
            }
            finally
            {
                if (_currentTransaction != null)
                {
                    _currentTransaction.Dispose();
                    _currentTransaction = null;
                }
            }
        }

        #endregion
    }
}
