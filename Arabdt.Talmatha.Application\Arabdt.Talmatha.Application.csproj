﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
    
  <ItemGroup>
    <Folder Include="Features\Company\DTOs\" />
    <Folder Include="Integrations\MCI\" />
    <Folder Include="Integrations\SocialInssurance\" />
  </ItemGroup>
    
  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="12.0.0" />
    <PackageReference Include="MediatR" Version="13.0.0" />
  </ItemGroup>
    
  <ItemGroup>
    <ProjectReference Include="..\Arabdt.Talmatha.Domain\Arabdt.Talmatha.Domain.csproj" />
  </ItemGroup>

</Project>
