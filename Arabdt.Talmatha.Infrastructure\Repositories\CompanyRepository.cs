﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Arabdt.Talmatha.Domain.Entities;
using Arabdt.Talmatha.Infrastructure.Context;
using Arabdt.Talmatha.SharedKernel;

namespace Arabdt.Talmatha.Infrastructure.Repositories
{
    public class CompanyRepository : Base.EntityRepository<Company>, Domain.Interfaces.ICompanyRepository
    {
        #region CTRS
        public IUnitOfWork UnitOfWork => AppDbContext;
        public CompanyRepository(IMapper mapper, TalmathaContext context) : base(context, mapper)
        { }
        #endregion
        public void AddCompany(Company company)
        {
            CreateAsyn(company);
        }
    }
}
