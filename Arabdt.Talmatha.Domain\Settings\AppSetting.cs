﻿namespace Arabdt.Talmatha.Domain.Settings
{
    public class AppSetting
    {
        public ConnectionStringsConfiguration ConnectionStrings { get; set; }
        public JwtConfiguration Jwt { get; set; }
        public ApiConfiguration Nafath { get; set; }
        public ApiConfiguration Mci { get; set; }
        public ApiConfiguration SocialInssurance { get; set; }
        public ApiConfiguration SMS { get; set; }
        public Swagger Swagger { get; set; }
        public EmailOptions EmailOptions { get; set; }
        public List<string> AllowedOrigins { get; set; }
    }

    public struct ConnectionStringsConfiguration
    {
        public string TalmathaDB { get; set; }
    }
    public class ApiConfiguration
    {
        public bool Enabled { get; set; }
        public string Url { get; set; }
        public string Provider { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public string ProductCode { get; set; }
        public string AppSid { get; set; }
        public string SenderID { get; set; }
        public string ApiKey { get; set; }
    }
    public struct JwtConfiguration
    {
        public string Key { get; set; }

        public string Issuer { get; set; }

        public string Audience { get; set; }

        public int RefreshTokenTimeOutInHours { get; set; }

        public int TokenTimeOutInMinutes { get; set; }

    }
    public struct Swagger
    {
        public bool Enabled { get; set; }
    }
    public class EmailOptions
    {
        public Sender Sender { get; set; }
        public string SmtpServer { get; set; }
        public int SmtpPort { get; set; }
        public string SmtpPassword { get; set; }
        public bool EnableSsl { get; set; }
    }
    public class Sender
    {
        public string Email { get; set; }
        public string Name { get; set; }
    }
}