﻿using MediatR;

namespace Arabdt.Talmatha.Application.Features.Company.Commands.AddCompany
{
    public class AddCompanyCommand : IRequest<bool>
    {
        #region Properties
        public int Id { get; set; }
        public string Name { get; set; }
        #endregion
        #region CTR
        public AddCompanyCommand(int id, string name)
        {
            Id = id;
            Name = name;
        }
        #endregion
    }
}
