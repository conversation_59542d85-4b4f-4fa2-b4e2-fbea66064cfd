# Talmatha.Backend Architecture Documentation

## Table of Contents
1. [High-Level Architecture](#high-level-architecture)
2. [System Overview](#system-overview)
3. [Technology Stack](#technology-stack)
4. [Project Structure](#project-structure)
5. [Low-Level Architecture](#low-level-architecture)
6. [Database Architecture](#database-architecture)
7. [API Design](#api-design)
8. [Authentication & Authorization](#authentication--authorization)
9. [External Integrations](#external-integrations)
10. [Configuration Management](#configuration-management)
11. [Setup Instructions](#setup-instructions)

## High-Level Architecture

### System Overview and Purpose
Talmatha.Backend is a .NET 8 Web API application built using Clean Architecture principles with Domain-Driven Design (DDD) patterns. The system serves as a backend platform for managing companies and integrating with external Saudi Arabian government services including MCI, Nafath, and Social Insurance.

### Architecture Pattern
The application follows **Clean Architecture** with clear separation of concerns across four main layers:

```mermaid
graph TB
    subgraph "Presentation Layer"
        API[Arabdt.Talmatha.Api]
    end
    
    subgraph "Application Layer"
        APP[Arabdt.Talmatha.Application]
    end
    
    subgraph "Domain Layer"
        DOM[Arabdt.Talmatha.Domain]
        SK[Arabdt.Talmatha.SharedKernel]
    end
    
    subgraph "Infrastructure Layer"
        INF[Arabdt.Talmatha.Infrastructure]
    end
    
    subgraph "External Services"
        DB[(SQL Server Database)]
        MCI[MCI Service]
        NAFATH[Nafath Service]
        SI[Social Insurance]
        SMS[SMS Service]
        EMAIL[Email Service]
    end
    
    API --> APP
    APP --> DOM
    APP --> SK
    INF --> DOM
    INF --> SK
    API --> INF
    
    INF --> DB
    INF --> MCI
    INF --> NAFATH
    INF --> SI
    INF --> SMS
    INF --> EMAIL
```

### Major Components and Responsibilities

#### 1. **Arabdt.Talmatha.Api** (Presentation Layer)
- **Responsibility**: HTTP request handling, routing, authentication, and response formatting
- **Key Components**:
  - Controllers (CompanyController)
  - Base classes (BaseController)
  - Dependency injection configuration (Autofac modules)
  - Middleware pipeline configuration
  - Swagger/OpenAPI documentation

#### 2. **Arabdt.Talmatha.Application** (Application Layer)
- **Responsibility**: Business logic orchestration, use cases, and application services
- **Key Components**:
  - CQRS Commands and Queries (MediatR)
  - Command/Query Handlers
  - Validation (FluentValidation)
  - External service integrations
  - DTOs and mapping

#### 3. **Arabdt.Talmatha.Domain** (Domain Layer)
- **Responsibility**: Core business entities, domain logic, and business rules
- **Key Components**:
  - Entities (Company, BaseEntity)
  - Domain interfaces (ICompanyRepository)
  - Enums (CompanyType)
  - Configuration settings (AppSetting)

#### 4. **Arabdt.Talmatha.Infrastructure** (Infrastructure Layer)
- **Responsibility**: Data persistence, external service implementations, and technical concerns
- **Key Components**:
  - Entity Framework DbContext (TalmathaContext)
  - Repository implementations
  - Entity configurations
  - Database migrations
  - AutoMapper profiles

#### 5. **Arabdt.Talmatha.SharedKernel** (Shared Kernel)
- **Responsibility**: Common interfaces, patterns, and utilities shared across layers
- **Key Components**:
  - Repository pattern interfaces (IRepository, IUnitOfWork)
  - Common validation utilities
  - Shared enums and patterns

## Technology Stack

### Core Framework
- **.NET 8.0**: Latest LTS version of .NET
- **ASP.NET Core Web API**: RESTful API framework
- **C# 12**: Latest language features with nullable reference types enabled

### Key Libraries and Frameworks

#### Dependency Injection & IoC
- **Autofac 8.4.0**: Advanced IoC container with module-based registration
- **Autofac.Extensions.DependencyInjection 10.0.0**: Integration with .NET DI

#### Data Access & ORM
- **Entity Framework Core 9.0.8**: Object-relational mapping
- **Microsoft.EntityFrameworkCore.SqlServer 9.0.8**: SQL Server provider
- **Microsoft.EntityFrameworkCore.Tools 9.0.8**: Migration and scaffolding tools

#### CQRS & Mediator Pattern
- **MediatR 13.0.0**: In-process messaging for CQRS implementation
- **FluentValidation 12.0.0**: Validation framework for commands and queries

#### Object Mapping
- **AutoMapper 15.0.1**: Object-to-object mapping

#### API Documentation
- **Swashbuckle.AspNetCore 6.6.2**: Swagger/OpenAPI documentation generation

### Database
- **SQL Server**: Primary database with connection retry policies
- **Entity Framework Migrations**: Database schema versioning

## Project Structure

```
Talmatha.Backend/
├── Arabdt.Talmatha.Api/                    # Presentation Layer
│   ├── Controllers/                        # API Controllers
│   │   └── CompanyController.cs
│   ├── Base/                              # Base classes and utilities
│   │   ├── AutofacHandlers/               # DI configuration
│   │   │   ├── ApplicationModule.cs       # Repository registrations
│   │   │   └── MediatorModule.cs          # MediatR registrations
│   │   ├── BaseController.cs              # Base controller class
│   │   ├── Routes.cs                      # Route constants
│   │   └── Behaviors/                     # Pipeline behaviors
│   ├── Program.cs                         # Application entry point
│   ├── appsettings.json                   # Configuration
│   └── Properties/
│
├── Arabdt.Talmatha.Application/            # Application Layer
│   ├── Features/                          # Feature-based organization
│   │   └── Company/                       # Company feature
│   │       ├── Commands/                  # Write operations
│   │       │   └── AddCompany/
│   │       │       ├── AddCompanyCommand.cs
│   │       │       ├── AddCompanyCommandHandler.cs
│   │       │       └── AddCompanyCommandValidator.cs
│   │       └── Queries/                   # Read operations
│   │           └── GetById/
│   └── Integrations/                      # External service integrations
│       ├── MCI/
│       └── SocialInssurance/
│
├── Arabdt.Talmatha.Domain/                 # Domain Layer
│   ├── Entities/                          # Domain entities
│   │   ├── Base/
│   │   │   └── BaseEntity.cs              # Base entity class
│   │   └── Company.cs                     # Company entity
│   ├── Interfaces/                        # Domain interfaces
│   │   └── ICompanyRepository.cs
│   ├── Enums/                            # Domain enumerations
│   │   └── CompanyType.cs
│   ├── Settings/                         # Configuration models
│   │   └── AppSetting.cs
│   └── DTOs/                             # Data transfer objects
│
├── Arabdt.Talmatha.Infrastructure/         # Infrastructure Layer
│   ├── Context/                          # Database context
│   │   ├── TalmathaContext.cs            # Main DbContext
│   │   └── TalmathaContextSeed.cs        # Data seeding
│   ├── EntityConfigurations/             # EF configurations
│   │   └── CompanyEntityTypeConfiguration.cs
│   ├── Repositories/                     # Repository implementations
│   │   ├── Base/
│   │   │   └── EntityRepository.cs       # Base repository
│   │   └── CompanyRepository.cs          # Company repository
│   ├── AutoMapper/                       # Mapping profiles
│   │   └── TalmathaProfile.cs
│   ├── Migrations/                       # EF migrations
│   ├── Seeds/                           # Data seeding
│   └── GlobalUsing.cs                   # Global using statements
│
└── Arabdt.Talmatha.SharedKernel/          # Shared Kernel
    ├── IRepository.cs                     # Repository interface
    ├── IUnitOfWork.cs                    # Unit of work interface
    ├── Patterns/                         # Common patterns
    ├── Validations/                      # Shared validations
    │   └── StringValidations.cs
    └── Enums/                           # Shared enumerations
```

## Low-Level Architecture

### Design Patterns and Principles

#### 1. **CQRS (Command Query Responsibility Segregation)**
The application implements CQRS using MediatR to separate read and write operations:

**Command Example:**
```csharp
// Command Definition
public class AddCompanyCommand : IRequest<bool>
{
    public int Id { get; set; }
    public string Name { get; set; }

    public AddCompanyCommand(int id, string name)
    {
        Id = id;
        Name = name;
    }
}

// Command Handler
public class AddCompanyCommandHandler : IRequestHandler<AddCompanyCommand, bool>
{
    private readonly ICompanyRepository _companyRepository;

    public AddCompanyCommandHandler(ICompanyRepository companyRepository)
    {
        _companyRepository = companyRepository;
    }

    public async Task<bool> Handle(AddCompanyCommand request, CancellationToken cancellationToken)
    {
        var company = new Domain.Entities.Company();
        company.CreateCompany(request.Name);
        _companyRepository.AddCompany(company);

        return await _companyRepository.UnitOfWork.SaveEntitiesAsync(cancellationToken);
    }
}
```

#### 2. **Repository Pattern with Unit of Work**
Data access is abstracted through repository pattern with Unit of Work for transaction management:

```csharp
// Repository Interface
public interface ICompanyRepository : IRepository<Company>
{
    void AddCompany(Company company);
}

// Repository Implementation
public class CompanyRepository : EntityRepository<Company>, ICompanyRepository
{
    public IUnitOfWork UnitOfWork => AppDbContext;

    public CompanyRepository(IMapper mapper, TalmathaContext context) : base(context, mapper)
    { }

    public void AddCompany(Company company)
    {
        CreateAsyn(company);
    }
}
```

#### 3. **Domain-Driven Design (DDD)**
Entities encapsulate business logic and maintain consistency:

```csharp
public class Company : BaseEntity
{
    public string Name { get; set; }

    public void CreateCompany(string name)
    {
        Name = name;
        CreatedAt = DateTime.Now;
    }
}

public class BaseEntity
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }
    public bool? IsDeleted { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? DeletedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
```

#### 4. **Dependency Injection with Autofac**
Advanced IoC configuration with module-based registration:

```csharp
// MediatR Module
public class MediatorModule : Autofac.Module
{
    protected override void Load(ContainerBuilder builder)
    {
        builder.RegisterAssemblyTypes(typeof(IMediator).GetTypeInfo().Assembly)
               .AsImplementedInterfaces();

        builder.RegisterAssemblyTypes(typeof(AddCompanyCommand).GetTypeInfo().Assembly)
               .AsClosedTypesOf(typeof(IRequestHandler<,>));

        builder.RegisterAssemblyTypes(typeof(AddCompanyCommandValidator).GetTypeInfo().Assembly)
               .Where(t => t.IsClosedTypeOf(typeof(IValidator<>)))
               .AsImplementedInterfaces();
    }
}

// Repository Module
public class ApplicationModule : Autofac.Module
{
    protected override void Load(ContainerBuilder builder)
    {
        builder.RegisterType<HttpContextAccessor>()
               .As<IHttpContextAccessor>()
               .InstancePerLifetimeScope();

        ResolveRepositories(builder);
    }

    private void ResolveRepositories(ContainerBuilder builder)
    {
        var repositories = Assembly.Load(typeof(CompanyRepository).Assembly.GetName()).GetTypes();
        var interfaces = Assembly.Load(typeof(ICompanyRepository).Assembly.GetName())
                                .GetTypes().Where(r => r.IsInterface);

        foreach (var repoInterface in interfaces)
        {
            var classType = repositories.FirstOrDefault(r => repoInterface.IsAssignableFrom(r));
            if (classType != null)
            {
                builder.RegisterType(classType).As(repoInterface).InstancePerLifetimeScope();
            }
        }
    }
}
```

### Data Flow Architecture

```mermaid
sequenceDiagram
    participant Client
    participant Controller
    participant MediatR
    participant Handler
    participant Repository
    participant DbContext
    participant Database

    Client->>Controller: HTTP POST /api/Company/Add
    Controller->>MediatR: Send(AddCompanyCommand)
    MediatR->>Handler: Handle(command)
    Handler->>Repository: AddCompany(entity)
    Repository->>DbContext: Add(entity)
    Handler->>Repository: UnitOfWork.SaveEntitiesAsync()
    Repository->>DbContext: SaveChangesAsync()
    DbContext->>Database: SQL INSERT
    Database-->>DbContext: Success
    DbContext-->>Repository: Result
    Repository-->>Handler: bool
    Handler-->>MediatR: bool
    MediatR-->>Controller: bool
    Controller-->>Client: HTTP 200 OK
```

## Database Architecture

### Database Schema

The application uses SQL Server with Entity Framework Core for data persistence. The current schema includes:

#### Company Table Structure
```sql
CREATE TABLE [Company] (
    [Id] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
    [Name] nvarchar(max) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [DeletedAt] datetime2 NULL,
    [IsDeleted] bit NULL
);
```

#### Entity Configuration
```csharp
public class CompanyEntityTypeConfiguration : IEntityTypeConfiguration<Company>
{
    public void Configure(EntityTypeBuilder<Company> builder)
    {
        builder.ToTable("Company");

        // Primary Key
        builder.HasKey(k => k.Id);
        builder.Property(p => p.Id)
               .HasColumnName("Id")
               .ValueGeneratedOnAdd()
               .IsRequired();

        // Properties
        builder.Property(p => p.Name).IsRequired();
        builder.Property(p => p.CreatedAt).IsRequired();
        builder.Property(p => p.UpdatedAt);
        builder.Property(p => p.DeletedAt);
        builder.Property(p => p.IsDeleted);
    }
}
```

### Database Context Configuration

```csharp
public class TalmathaContext : DbContext, IUnitOfWork
{
    public DbSet<Company> Companies { get; set; }

    public TalmathaContext(DbContextOptions<TalmathaContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfiguration(new CompanyEntityTypeConfiguration());
        modelBuilder.BuildEnums();
    }

    // Unit of Work Implementation
    public async Task<bool> SaveEntitiesAsync(CancellationToken cancellationToken = default)
    {
        var result = await base.SaveChangesAsync(cancellationToken) > 0;
        // Domain events can be dispatched here
        return result;
    }

    // Transaction Management
    private IDbContextTransaction _currentTransaction;
    public IDbContextTransaction GetCurrentTransaction() => _currentTransaction;
    public bool HasActiveTransaction => _currentTransaction != null;

    public async Task<IDbContextTransaction> BeginTransactionAsync()
    {
        if (_currentTransaction != null) return null;

        _currentTransaction = await Database.BeginTransactionAsync(IsolationLevel.ReadCommitted);
        return _currentTransaction;
    }
}
```

### Connection Configuration
```csharp
// Program.cs - Database Configuration
builder.Services.AddDbContext<TalmathaContext>(options =>
{
    options.UseSqlServer(appSettings.ConnectionStrings.TalmathaDB,
        sqlServerOptionsAction: sqlOptions =>
        {
            sqlOptions.MigrationsAssembly("Arabdt.Talmatha.Infrastructure");
            sqlOptions.EnableRetryOnFailure(
                maxRetryCount: 15,
                maxRetryDelay: TimeSpan.FromSeconds(30),
                errorNumbersToAdd: null);
        });
}, ServiceLifetime.Scoped);
```

## API Design

### RESTful API Endpoints

#### Current Endpoints
| Method | Endpoint | Description | Request Body | Response |
|--------|----------|-------------|--------------|----------|
| POST | `/api/Company/Add` | Create new company | `AddCompanyCommand` | `bool` |

#### API Controller Structure
```csharp
[Authorize]
[ApiController]
[Route(Routes.API)] // "api/[controller]/[action]"
public class CompanyController : BaseController
{
    public CompanyController(IMediator mediator) : base(mediator)
    { }

    [HttpPost]
    public async Task<IActionResult> Add([FromBody] AddCompanyCommand addCompanyCommand)
    {
        return Ok(await Mediator.Send(addCompanyCommand));
    }
}
```

#### Base Controller Pattern
```csharp
public class BaseController : ControllerBase
{
    protected IMediator Mediator { get; }

    public BaseController(IMediator mediator)
    {
        Mediator = mediator;
    }
}
```

### API Documentation (Swagger/OpenAPI)

The API includes comprehensive Swagger documentation configured in `Program.cs`:

```csharp
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Arabdt.Talmatha.Api", Version = "v1" });
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Name = "Authorization",
        Scheme = "Bearer",
        Description = "JWT bearer authentication token.",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
    });
    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});
```

### Request/Response Models

#### Command Models
```csharp
public class AddCompanyCommand : IRequest<bool>
{
    public int Id { get; set; }
    public string Name { get; set; }

    public AddCompanyCommand(int id, string name)
    {
        Id = id;
        Name = name;
    }
}
```

### Validation Framework

FluentValidation is used for request validation:

```csharp
public class AddCompanyCommandValidator : AbstractValidator<AddCompanyCommand>
{
    public AddCompanyCommandValidator()
    {
        RuleFor(command => command.Name)
            .NotNull()
            .WithMessage("Name is required.");
    }
}
```

