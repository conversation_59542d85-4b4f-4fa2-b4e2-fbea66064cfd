﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Arabdt.Talmatha.Domain.Entities.Base;

namespace Arabdt.Talmatha.Domain.Entities
{
    public class Company: BaseEntity
    {
        public string Name { get; set; }
        public void CreateCompany(string name)
        {
            Name = name;
            CreatedAt = DateTime.Now;
        }
    }
}
