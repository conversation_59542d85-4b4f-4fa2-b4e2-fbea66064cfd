{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"TalmathaDb": "Server=.;Database=Talmatha_Dev;User Id=sa; Password=;TrustServerCertificate=True;MultipleActiveResultSets=True"}, "Swagger": {"Enabled": true}, "Jwt": {"Key": "", "Issuer": "<PERSON><PERSON><PERSON>", "Audience": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RefreshTokenTimeOutInHours": 8, "TokenTimeOutInMinutes": 30}, "Mci": {"Enabled": true, "Provider": "", "Url": "", "ApiKey": ""}, "Nafath": {"Enabled": true, "Provider": "", "Url": "", "ApiKey": ""}, "SocialInssurence": {"Enabled": true, "Provider": "", "Url": "", "ApiKey": ""}, "SMS": {"Enabled": false, "Provider": "", "Url": "", "AppSid": "", "SenderID": ""}, "AllowedOrigins": ["http://localhost:4200"], "EmailOptions": {"Sender": {"Email": "", "Name": ""}, "SmtpServer": "", "SmtpPort": 587, "SmtpPassword": "", "EnableSsl": true}}