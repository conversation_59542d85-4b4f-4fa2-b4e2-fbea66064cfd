﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FluentValidation;

namespace Arabdt.Talmatha.Application.Features.Company.Commands.AddCompany
{
    public class AddCompanyCommandValidator : AbstractValidator<AddCompanyCommand>
    {
        public AddCompanyCommandValidator()
        {
            RuleFor(command => command.Name).NotNull().WithMessage("Name is required.");
        }
    }
}
