﻿namespace Arabdt.Talmatha.Infrastructure.AutoMapper
{
    internal class TalmathaProfile : Profile
    {
        public TalmathaProfile()
        {
            //StoryCategoryMapping();
          
        }
        #region Private Methods
        //private void StoryCategoryMapping()
        //{


        //    CreateMap<Domain.StoryCategory.Entity.StoryCategory, Domain.StoryCategory.Dto.StoryCategoryDto>();
        //    CreateMap<Domain.StoryCategory.Entity.StoryCategory, DropDownItem<long>>();

        //    CreateMap<Domain.StoryCategory.Entity.StoryCategory, Domain.StoryCategory.Dto.StoryCategoryWithAssignedFlagDto>();

        //    CreateMap<Domain.StoryCategory.ValueObject.StoryCategoryLocalization, Domain.StoryCategory.Dto.StoryCategoryLocalizationDto>();
        //    CreateMap<Domain.StoryCategory.Entity.StoryCategory, Domain._SharedKernel.Dto.FrontEndSectionTypeDto>()
        //       .ConvertUsing(storyCategory => new Domain._SharedKernel.Dto.FrontEndSectionTypeDto()
        //       {
        //           Name = storyCategory.Name,
        //           Alias = storyCategory.Alias,
        //           CssClass = string.Empty,
        //           MailStyle = string.Empty,
        //           FrontEndSectionTypeDetailList = storyCategory.StoryCategoryLocalization.Select(l => new FrontEndSectionTypeDetailDto()
        //           {
        //               LanguageId = l.LanguageId,
        //               LogoUrl = string.Empty,
        //               Title = l.Name
        //           }).ToList()
        //       });
        //}
        
        #endregion
    }
}
