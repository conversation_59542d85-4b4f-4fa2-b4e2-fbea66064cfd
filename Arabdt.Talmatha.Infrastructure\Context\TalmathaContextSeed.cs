﻿namespace Arabdt.Talmatha.Infrastructure.Context
{
    public static class TalmathaContextSeed
    {
        public static void BuildEnums(this ModelBuilder modelBuilder)
        {
            #region Story Types
            //modelBuilder.Entity<StoryType>().HasData(new StoryType(StoryTypeEnum.NormalStory.Id, StoryTypeEnum.NormalStory.Name));
            #endregion
            #region Website
            //modelBuilder.Entity<Website>().HasData(new Website(1, WebsiteEnum.News.Name, "0B4LQwgr0BcpyLTdQYktPVzBMOWc"));
            //modelBuilder.Entity<Website>().HasData(new Website(2, WebsiteEnum.Logistics.Name, "0B4LQwgr0BcpyLTdQYktPVzBMOWc"));
            //modelBuilder.Entity<Website>().HasData(new Website(3, WebsiteEnum.Climate.Name, "0B4LQwgr0BcpyLTdQYktPVzBMOWc"));
            //modelBuilder.Entity<Website>().HasData(new Website(4, WebsiteEnum.NewsUAE.Name, "0ANzxOWGHqqihUk9PVA"));
            //modelBuilder.Entity<Website>().HasData(new Website(5, WebsiteEnum.NewsKSA.Name, "0ALplRiJDCzRcUk9PVA"));
            //modelBuilder.Entity<Website>().HasData(new Website(6, WebsiteEnum.Enterprise1.Name, "0ALplRiJDCzRcUk9PVA"));

            #endregion
        }
    }
}
