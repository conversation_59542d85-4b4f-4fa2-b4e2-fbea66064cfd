﻿using Arabdt.Talmatha.Domain.Entities;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Arabdt.Talmatha.Infrastructure.EntityConfigurations
{
    public class CompanyEntityTypeConfiguration : IEntityTypeConfiguration<Company>
    {
        public void Configure(EntityTypeBuilder<Company> builder)
        {
            builder.ToTable("Company");
            //Key
            builder.HasKey(k => k.Id);
            builder.Property(p => p.Id).HasColumnName("Id").ValueGeneratedOnAdd().IsRequired();
        }
    }
}
