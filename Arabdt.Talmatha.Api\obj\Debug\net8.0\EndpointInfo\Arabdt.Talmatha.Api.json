{"openapi": "3.0.1", "info": {"title": "Arabdt.Talmatha.Api", "version": "v1"}, "paths": {"/api/Company/Add": {"post": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddCompanyCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddCompanyCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddCompanyCommand"}}}}, "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"AddCompanyCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT bearer authentication token.", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}