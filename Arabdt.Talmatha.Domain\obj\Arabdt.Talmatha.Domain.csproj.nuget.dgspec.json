{"format": 1, "restore": {"C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Domain\\Arabdt.Talmatha.Domain.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Domain\\Arabdt.Talmatha.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Domain\\Arabdt.Talmatha.Domain.csproj", "projectName": "Arabdt.Talmatha.Domain", "projectPath": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Domain\\Arabdt.Talmatha.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.SharedKernel\\Arabdt.Talmatha.SharedKernel.csproj": {"projectPath": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.SharedKernel\\Arabdt.Talmatha.SharedKernel.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.SharedKernel\\Arabdt.Talmatha.SharedKernel.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.SharedKernel\\Arabdt.Talmatha.SharedKernel.csproj", "projectName": "Arabdt.Talmatha.SharedKernel", "projectPath": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.SharedKernel\\Arabdt.Talmatha.SharedKernel.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.SharedKernel\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}}}