﻿using Arabdt.Talmatha.Domain.Interfaces;
using MediatR;

namespace Arabdt.Talmatha.Application.Features.Company.Commands.AddCompany
{
    public class AddCompanyCommandHandler : IRequestHandler<AddCompanyCommand, bool>
    {
        #region Props
        private readonly ICompanyRepository _companyRepository;
        #endregion
        #region CTRS
        public AddCompanyCommandHandler(ICompanyRepository companyRepository)
        {
            _companyRepository = companyRepository;
        }
        #endregion
        public async Task<bool> Handle(AddCompanyCommand request, CancellationToken cancellationToken)
        {
            bool response = default;
            Domain.Entities.Company company = new();
            company.CreateCompany(request.Name);
            _companyRepository.AddCompany(company);

            if (await _companyRepository.UnitOfWork.SaveEntitiesAsync(cancellationToken))
            {
                response = true;
            }
            return response;
        }
    }

}
