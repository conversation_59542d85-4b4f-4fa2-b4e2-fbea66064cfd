{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Api\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A12B8587-7BFD-4C5F-9AAD-9A7A47FC2EA7}|Arabdt.Talmatha.Api.csproj|c:\\users\\<USER>\\work\\talmatha.backend\\arabdt.talmatha.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A12B8587-7BFD-4C5F-9AAD-9A7A47FC2EA7}|Arabdt.Talmatha.Api.csproj|solutionrelative:program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9400C753-019D-43B6-82E5-C5BB017746BB}|..\\Arabdt.Talmatha.Domain\\Arabdt.Talmatha.Domain.csproj|c:\\users\\<USER>\\work\\talmatha.backend\\arabdt.talmatha.domain\\arabdt.talmatha.domain.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{F12FFE11-1D9A-4E78-B3B4-6C8CCE434BDC}|..\\Arabdt.Talmatha.Infrastructure\\Arabdt.Talmatha.Infrastructure.csproj|c:\\users\\<USER>\\work\\talmatha.backend\\arabdt.talmatha.infrastructure\\arabdt.talmatha.infrastructure.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\32c37e5c-5e6a-43c8-95dc-b921927d0d0e_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\07a73162-ee71-4577-bf14-43e66013934f_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A12B8587-7BFD-4C5F-9AAD-9A7A47FC2EA7}|Arabdt.Talmatha.Api.csproj|c:\\users\\<USER>\\work\\talmatha.backend\\arabdt.talmatha.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A12B8587-7BFD-4C5F-9AAD-9A7A47FC2EA7}|Arabdt.Talmatha.Api.csproj|solutionrelative:appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{F12FFE11-1D9A-4E78-B3B4-6C8CCE434BDC}|..\\Arabdt.Talmatha.Infrastructure\\Arabdt.Talmatha.Infrastructure.csproj|c:\\users\\<USER>\\work\\talmatha.backend\\arabdt.talmatha.infrastructure\\context\\talmathacontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\a0715d6b-aecc-4372-bcb1-cf5ca1a3807c_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Infrastructure\\Mediator\\MediatorExtension.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F12FFE11-1D9A-4E78-B3B4-6C8CCE434BDC}|..\\Arabdt.Talmatha.Infrastructure\\Arabdt.Talmatha.Infrastructure.csproj|c:\\users\\<USER>\\work\\talmatha.backend\\arabdt.talmatha.infrastructure\\entityconfigurations\\companyentitytypeconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{015F0948-5B2B-48C3-B82E-7F5B52FA3249}|..\\Arabdt.Talmatha.Application\\Arabdt.Talmatha.Application.csproj|c:\\users\\<USER>\\work\\talmatha.backend\\arabdt.talmatha.application\\features\\company\\commands\\addcompany\\addcompanycommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F12FFE11-1D9A-4E78-B3B4-6C8CCE434BDC}|..\\Arabdt.Talmatha.Infrastructure\\Arabdt.Talmatha.Infrastructure.csproj|c:\\users\\<USER>\\work\\talmatha.backend\\arabdt.talmatha.infrastructure\\repositories\\companyrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\8720483c-6d31-468d-b350-a0f89f69a358_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Domain\\MarketPrice\\Repository\\IMarketPriceRepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\46561bfb-c5f8-488a-aede-156245e1cf67_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Infrastructure\\Repositories\\MarketPriceRepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A12B8587-7BFD-4C5F-9AAD-9A7A47FC2EA7}|Arabdt.Talmatha.Api.csproj|c:\\users\\<USER>\\work\\talmatha.backend\\arabdt.talmatha.api\\controllers\\companycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A12B8587-7BFD-4C5F-9AAD-9A7A47FC2EA7}|Arabdt.Talmatha.Api.csproj|solutionrelative:controllers\\companycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{015F0948-5B2B-48C3-B82E-7F5B52FA3249}|..\\Arabdt.Talmatha.Application\\Arabdt.Talmatha.Application.csproj|c:\\users\\<USER>\\work\\talmatha.backend\\arabdt.talmatha.application\\features\\company\\commands\\addcompany\\addcompanycommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\98a6c6cf-219a-45f1-a1a0-a40ce696b17c_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.AppService\\MarketPrice\\AddEditMarketPrice\\AddEditMarketPriceCommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\30f53ba7-93de-49c2-9cfd-c1014d911114_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Infrastructure\\Base\\BaseController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\d7768d8b-6758-4219-b4c1-6d9777229d2e_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\GlobalUsing.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\d0995e12-a479-4592-bc7c-b74671ea0bb2_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Infrastructure\\AutofacHandler\\ApplicationModule.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 12, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "Arabdt.Talmatha.Infrastructure", "DocumentMoniker": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Infrastructure\\Arabdt.Talmatha.Infrastructure.csproj", "RelativeDocumentMoniker": "..\\Arabdt.Talmatha.Infrastructure\\Arabdt.Talmatha.Infrastructure.csproj", "ToolTip": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Infrastructure\\Arabdt.Talmatha.Infrastructure.csproj", "RelativeToolTip": "..\\Arabdt.Talmatha.Infrastructure\\Arabdt.Talmatha.Infrastructure.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-08-17T16:29:12.705Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c64b9c2-e352-428e-a56d-0ace190b99a6}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:133:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:135:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:138:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "Arabdt.Talmatha.Domain", "DocumentMoniker": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Domain\\Arabdt.Talmatha.Domain.csproj", "RelativeDocumentMoniker": "..\\Arabdt.Talmatha.Domain\\Arabdt.Talmatha.Domain.csproj", "ToolTip": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Domain\\Arabdt.Talmatha.Domain.csproj", "RelativeToolTip": "..\\Arabdt.Talmatha.Domain\\Arabdt.Talmatha.Domain.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-08-17T15:47:13.221Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\32c37e5c-5e6a-43c8-95dc-b921927d0d0e_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Program.cs", "RelativeDocumentMoniker": "..\\..\\..\\AppData\\Local\\Temp\\32c37e5c-5e6a-43c8-95dc-b921927d0d0e_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\32c37e5c-5e6a-43c8-95dc-b921927d0d0e_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Program.cs [Read Only]", "RelativeToolTip": "..\\..\\..\\AppData\\Local\\Temp\\32c37e5c-5e6a-43c8-95dc-b921927d0d0e_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Program.cs [Read Only]", "ViewState": "AgIAAAkBAAAAAAAAAAAAAMoAAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-17T15:44:04.9Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Api\\Program.cs", "RelativeDocumentMoniker": "Program.cs", "ToolTip": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Api\\Program.cs", "RelativeToolTip": "Program.cs", "ViewState": "AgIAAEMAAAAAAAAAAAAYwE0AAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-17T15:43:37.493Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "MediatorExtension.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\a0715d6b-aecc-4372-bcb1-cf5ca1a3807c_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Infrastructure\\Mediator\\MediatorExtension.cs", "RelativeDocumentMoniker": "..\\..\\..\\AppData\\Local\\Temp\\a0715d6b-aecc-4372-bcb1-cf5ca1a3807c_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Infrastructure\\Mediator\\MediatorExtension.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\a0715d6b-aecc-4372-bcb1-cf5ca1a3807c_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Infrastructure\\Mediator\\MediatorExtension.cs [Read Only]", "RelativeToolTip": "..\\..\\..\\AppData\\Local\\Temp\\a0715d6b-aecc-4372-bcb1-cf5ca1a3807c_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Infrastructure\\Mediator\\MediatorExtension.cs [Read Only]", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAkAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-17T15:26:05.505Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 5, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Api\\appsettings.json", "RelativeDocumentMoniker": "appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Api\\appsettings.json", "RelativeToolTip": "appsettings.json", "ViewState": "AgIAACcAAAAAAAAAAAAAAC4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-17T15:52:25.965Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\07a73162-ee71-4577-bf14-43e66013934f_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Program.cs", "RelativeDocumentMoniker": "..\\..\\..\\AppData\\Local\\Temp\\07a73162-ee71-4577-bf14-43e66013934f_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\07a73162-ee71-4577-bf14-43e66013934f_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Program.cs [Read Only]", "RelativeToolTip": "..\\..\\..\\AppData\\Local\\Temp\\07a73162-ee71-4577-bf14-43e66013934f_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Program.cs [Read Only]", "ViewState": "AgIAAGAAAAAAAAAAAAAAAIcAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-17T16:02:43.016Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 8, "Title": "CompanyEntityTypeConfiguration.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Infrastructure\\EntityConfigurations\\CompanyEntityTypeConfiguration.cs", "RelativeDocumentMoniker": "..\\Arabdt.Talmatha.Infrastructure\\EntityConfigurations\\CompanyEntityTypeConfiguration.cs", "ToolTip": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Infrastructure\\EntityConfigurations\\CompanyEntityTypeConfiguration.cs", "RelativeToolTip": "..\\Arabdt.Talmatha.Infrastructure\\EntityConfigurations\\CompanyEntityTypeConfiguration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-17T13:31:00.425Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "TalmathaContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Infrastructure\\Context\\TalmathaContext.cs", "RelativeDocumentMoniker": "..\\Arabdt.Talmatha.Infrastructure\\Context\\TalmathaContext.cs", "ToolTip": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Infrastructure\\Context\\TalmathaContext.cs", "RelativeToolTip": "..\\Arabdt.Talmatha.Infrastructure\\Context\\TalmathaContext.cs", "ViewState": "AgIAADAAAAAAAAAAAAAWwDcAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-17T13:30:08.946Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "IMarketPriceRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\8720483c-6d31-468d-b350-a0f89f69a358_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Domain\\MarketPrice\\Repository\\IMarketPriceRepository.cs", "RelativeDocumentMoniker": "..\\..\\..\\AppData\\Local\\Temp\\8720483c-6d31-468d-b350-a0f89f69a358_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Domain\\MarketPrice\\Repository\\IMarketPriceRepository.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\8720483c-6d31-468d-b350-a0f89f69a358_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Domain\\MarketPrice\\Repository\\IMarketPriceRepository.cs [Read Only]", "RelativeToolTip": "..\\..\\..\\AppData\\Local\\Temp\\8720483c-6d31-468d-b350-a0f89f69a358_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Domain\\MarketPrice\\Repository\\IMarketPriceRepository.cs [Read Only]", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-17T13:19:32.328Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 12, "Title": "MarketPriceRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\46561bfb-c5f8-488a-aede-156245e1cf67_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Infrastructure\\Repositories\\MarketPriceRepository.cs", "RelativeDocumentMoniker": "..\\..\\..\\AppData\\Local\\Temp\\46561bfb-c5f8-488a-aede-156245e1cf67_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Infrastructure\\Repositories\\MarketPriceRepository.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\46561bfb-c5f8-488a-aede-156245e1cf67_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Infrastructure\\Repositories\\MarketPriceRepository.cs [Read Only]", "RelativeToolTip": "..\\..\\..\\AppData\\Local\\Temp\\46561bfb-c5f8-488a-aede-156245e1cf67_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Infrastructure\\Repositories\\MarketPriceRepository.cs [Read Only]", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAABjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-17T13:19:01.561Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 10, "Title": "CompanyRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Infrastructure\\Repositories\\CompanyRepository.cs", "RelativeDocumentMoniker": "..\\Arabdt.Talmatha.Infrastructure\\Repositories\\CompanyRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Infrastructure\\Repositories\\CompanyRepository.cs", "RelativeToolTip": "..\\Arabdt.Talmatha.Infrastructure\\Repositories\\CompanyRepository.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAAABQAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-17T13:12:43.272Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "AddCompanyCommandHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Application\\Features\\Company\\Commands\\AddCompany\\AddCompanyCommandHandler.cs", "RelativeDocumentMoniker": "..\\Arabdt.Talmatha.Application\\Features\\Company\\Commands\\AddCompany\\AddCompanyCommandHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Application\\Features\\Company\\Commands\\AddCompany\\AddCompanyCommandHandler.cs", "RelativeToolTip": "..\\Arabdt.Talmatha.Application\\Features\\Company\\Commands\\AddCompany\\AddCompanyCommandHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-17T12:50:43.245Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "AddEditMarketPriceCommand.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\98a6c6cf-219a-45f1-a1a0-a40ce696b17c_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.AppService\\MarketPrice\\AddEditMarketPrice\\AddEditMarketPriceCommand.cs", "RelativeDocumentMoniker": "..\\..\\..\\AppData\\Local\\Temp\\98a6c6cf-219a-45f1-a1a0-a40ce696b17c_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.AppService\\MarketPrice\\AddEditMarketPrice\\AddEditMarketPriceCommand.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\98a6c6cf-219a-45f1-a1a0-a40ce696b17c_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.AppService\\MarketPrice\\AddEditMarketPrice\\AddEditMarketPriceCommand.cs [Read Only]", "RelativeToolTip": "..\\..\\..\\AppData\\Local\\Temp\\98a6c6cf-219a-45f1-a1a0-a40ce696b17c_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.AppService\\MarketPrice\\AddEditMarketPrice\\AddEditMarketPriceCommand.cs [Read Only]", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAAAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-17T12:48:05.313Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 14, "Title": "AddCompanyCommand.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Application\\Features\\Company\\Commands\\AddCompany\\AddCompanyCommand.cs", "RelativeDocumentMoniker": "..\\Arabdt.Talmatha.Application\\Features\\Company\\Commands\\AddCompany\\AddCompanyCommand.cs", "ToolTip": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Application\\Features\\Company\\Commands\\AddCompany\\AddCompanyCommand.cs", "RelativeToolTip": "..\\Arabdt.Talmatha.Application\\Features\\Company\\Commands\\AddCompany\\AddCompanyCommand.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAA4AAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-17T12:47:45.935Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "BaseController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\30f53ba7-93de-49c2-9cfd-c1014d911114_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Infrastructure\\Base\\BaseController.cs", "RelativeDocumentMoniker": "..\\..\\..\\AppData\\Local\\Temp\\30f53ba7-93de-49c2-9cfd-c1014d911114_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Infrastructure\\Base\\BaseController.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\30f53ba7-93de-49c2-9cfd-c1014d911114_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Infrastructure\\Base\\BaseController.cs [Read Only]", "RelativeToolTip": "..\\..\\..\\AppData\\Local\\Temp\\30f53ba7-93de-49c2-9cfd-c1014d911114_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Infrastructure\\Base\\BaseController.cs [Read Only]", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-17T12:38:05.181Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 17, "Title": "GlobalUsing.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\d7768d8b-6758-4219-b4c1-6d9777229d2e_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\GlobalUsing.cs", "RelativeDocumentMoniker": "..\\..\\..\\AppData\\Local\\Temp\\d7768d8b-6758-4219-b4c1-6d9777229d2e_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\GlobalUsing.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\d7768d8b-6758-4219-b4c1-6d9777229d2e_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\GlobalUsing.cs [Read Only]", "RelativeToolTip": "..\\..\\..\\AppData\\Local\\Temp\\d7768d8b-6758-4219-b4c1-6d9777229d2e_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\GlobalUsing.cs [Read Only]", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-17T12:35:37.94Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 13, "Title": "CompanyController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Api\\Controllers\\CompanyController.cs", "RelativeDocumentMoniker": "Controllers\\CompanyController.cs", "ToolTip": "C:\\Users\\<USER>\\Work\\Talmatha.Backend\\Arabdt.Talmatha.Api\\Controllers\\CompanyController.cs", "RelativeToolTip": "Controllers\\CompanyController.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAWwBIAAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-17T12:35:10.142Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "ApplicationModule.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\d0995e12-a479-4592-bc7c-b74671ea0bb2_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Infrastructure\\AutofacHandler\\ApplicationModule.cs", "RelativeDocumentMoniker": "..\\..\\..\\AppData\\Local\\Temp\\d0995e12-a479-4592-bc7c-b74671ea0bb2_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Infrastructure\\AutofacHandler\\ApplicationModule.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\d0995e12-a479-4592-bc7c-b74671ea0bb2_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Infrastructure\\AutofacHandler\\ApplicationModule.cs [Read Only]", "RelativeToolTip": "..\\..\\..\\AppData\\Local\\Temp\\d0995e12-a479-4592-bc7c-b74671ea0bb2_Publisher-api.rar.Publisher-api.rar\\Publisher-api\\src\\Enterprise.Publisher.Api\\Infrastructure\\AutofacHandler\\ApplicationModule.cs [Read Only]", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-17T12:33:55.114Z", "EditorCaption": " [Read Only]"}]}]}]}